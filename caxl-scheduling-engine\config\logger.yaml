# Logger Configuration for Healthcare Appointment Scheduler
# This file controls logging levels and output formatting

# Global logging configuration
version: 1
disable_existing_loggers: false

# Formatters
formatters:
  standard:
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
  detailed:
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  simple:
    format: "{time:HH:mm:ss} | {level} | {message}"

# Handlers
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: simple
    stream: ext://sys.stdout
  
  file:
    class: logging.FileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/scheduler_{time:YYYY-MM-DD}.log
    mode: a
  
  error_file:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: logs/errors_{time:YYYY-MM-DD}.log
    mode: a

# Loggers - Application specific
loggers:
  # Main application logger
  appointment_scheduler:
    level: INFO
    handlers: [console, file]
    propagate: false
  
  # Healthcare sequencing constraint logger
  appointment_scheduler.constraints.c013_schd_healthcare_task_sequencing:
    level: INFO  # Set to INFO to see only summary healthcare sequencing analysis
    handlers: [console, file]
    propagate: false
  
  # Day plan job logger
  appointment_scheduler.jobs.day_plan:
    level: INFO
    handlers: [console, file]
    propagate: false
  
  # Assignment job logger
  appointment_scheduler.jobs.assign_appointments:
    level: INFO
    handlers: [console, file]
    propagate: false
  
  # Timefold solver logger
  timefold:
    level: WARNING  # Reduce Timefold solver verbosity
    handlers: [file]
    propagate: false
  
  # Third-party libraries
  urllib3:
    level: WARNING
    handlers: [file]
    propagate: false
  
  requests:
    level: WARNING
    handlers: [file]
    propagate: false

# Root logger configuration
root:
  level: INFO
  handlers: [console, file, error_file]

# Logging levels reference:
# DEBUG: Detailed information for debugging
# INFO: General information about program execution
# WARNING: Indicate a potential problem
# ERROR: A more serious problem
# CRITICAL: A critical problem that may prevent the program from running

# To change healthcare sequencing constraint logging:
# - Set level to DEBUG to see detailed penalty calculations
# - Set level to INFO to see only summary information
# - Set level to WARNING to suppress most healthcare sequencing logs 