# CAXL Scheduling Engine Documentation Summary

## Excel Documentation Generated

**File:** `CAXL_Scheduling_Engine_Documentation_v2.xlsx`

## Documentation Structure
The Excel file contains 4 tabs:
1. **Domain Model** (hierarchical structure with field usage)
2. **Constraints** (c001-c016 implementation details)
3. **Global Configuration** (scheduler settings)
4. **Service Configuration & Mapping** (service-specific settings)

## Key Corrections Made

### ProviderAvailability Field Usage Corrections
The following fields were incorrectly marked as "Display/Logging only" but are actually used in constraints:

| Field | Previous Status | Corrected Status | Usage |
|-------|----------------|------------------|-------|
| `primary_shift` | Display/Logging only | ✅ **USED** | c002 (via get_shift_hours) |
| `additional_shifts` | Display/Logging only | ✅ **USED** | c002 (via get_shift_hours) |
| `split_shifts` | Display/Logging only | ✅ **USED** | c002 (via get_shift_hours) |
| `working_hours` | Display/Logging only | ✅ **USED** | c002 (via get_shift_hours) |
| `date_specific_availability` | Display/Logging only | ✅ **USED** | c002 (via get_shift_hours) |
| `time_off_periods` | Display/Logging only | ✅ **USED** | c002 (direct usage) |
| `working_days` | Display/Logging only | ✅ **USED** | c002, base_constraints |
| `break_periods` | Display/Logging only | ✅ **USED** | c010, c012 (via is_available_at_time) |

### Missing Domain Model Classes Added
The following classes were missing from the domain model documentation:

#### Location Class Fields
| Field | Type | Required | Usage | Example |
|-------|------|----------|-------|---------|
| `latitude` | float | Yes | c003, c007 | 40.7128 |
| `longitude` | float | Yes | c003, c007 | -74.0060 |
| `address` | str | No | c007 | "123 Main St" |
| `city` | str | No | c007 | "New York" |
| `state` | str | No | c003 | "NY" |
| `country` | str | No | Display only | "USA" |
| `zip_code` | str | No | Display only | "10001" |

#### Geofence Class Fields
| Field | Type | Required | Usage | Example |
|-------|------|----------|-------|---------|
| `id` | int | No | Display only | 1 |
| `name` | str | No | Display only | "Brooklyn Heights" |
| `boundary_wkt` | str | No | c003 | "POLYGON((-73.9961 40.6955, ...))" |
| `zone_type` | str | Yes | c003 | "service" |
| `description` | str | No | Display only | "Service area for Brooklyn" |
| `priority` | int | No | c003 | 1 |
| `reason` | str | No | c003 | "unsafe_area" |
| `severity` | str | No | c003 | "hard" |

#### DateSpecificProviderAvailability Class Fields
| Field | Type | Required | Usage | Example |
|-------|------|----------|-------|---------|
| `date` | date | Yes | c002 (via get_shift_hours) | date.today() |
| `available_start` | time | No | c002 (via get_shift_hours) | time(9, 0) |
| `available_end` | time | No | c002 (via get_shift_hours) | time(17, 0) |
| `reason` | str | No | Display only | "half_day_leave" |

### C002 Constraint Field Usage Summary
The `c002_asgn_date_based_availability` constraint uses the following ProviderAvailability fields:

#### Direct Usage:
- `time_off_periods` - Vacation/sick leave periods
- `working_days` - Days provider is scheduled to work

#### Via `get_shift_hours()` method:
- `date_specific_availability` - Date-specific overrides
- `time_off_periods` - Time off periods (checked again)
- `primary_shift.shift_days` - Primary shift working days
- `additional_shifts[].shift_days` - Additional shift working days
- `working_hours` - Simple hour ranges
- `split_shifts` - Multiple shifts per day

## Color Coding in Excel
- **White background**: Fields used in constraints
- **Gray background**: Display/Logging only fields

## Domain Model Coverage
The documentation now includes comprehensive coverage of:
- **Location** and **Geofence** classes with all their fields
- **DateSpecificProviderAvailability** class with all its fields
- **Provider** and related models (ProviderAvailability, ProviderCapacity, ProviderPreferences)
- **Consumer** and ConsumerPreferences
- **Appointment** models (AppointmentData, AppointmentTiming, AppointmentRelationships, AppointmentPinning)
- **Planning entities** (AppointmentAssignment, ScheduledAppointment, TimeSlotAssignment)
- **Solution models** (AppointmentSchedule, DaySchedule)
- **Configuration models** (ServiceConfig, SchedulerConfig)

## Constraint Documentation
All 16 constraints (c001-c016) are documented with:
- Function names and descriptions
- Implementation details
- Usage examples

## Configuration Documentation
- **Global Configuration**: Scheduler-wide settings and feature toggles
- **Service Configuration**: Service-specific parameters and mappings

## File Location
The generated Excel file is located at: `D:\Work\Scheduler\CAXL_Scheduling_Engine_Documentation_v2.xlsx`

## Notes
- All field usage information has been verified against actual constraint implementations
- The documentation accurately reflects which fields are used in constraints vs. display/logging only
- The hierarchical structure shows the relationship between domain models
- Color coding helps distinguish between constraint-used and display-only fields
- **Complete coverage**: All domain model classes and their fields are now documented

### Tab 1: Domain Model (Hierarchical)
- **Structure:** Domain | Field | Type | Required | Description | Field Usage | Example
- **Content:** Comprehensive field-level documentation for all domain entities with **accurate constraint usage**
- **Color Coding:** Gray background for fields used only for display/logging (not in constraints)
- **Domain Entities Covered:**
  - Provider (16 fields) - **Accurate Usage:** Only 8 fields actually used in constraints
  - ProviderAvailability (18 fields) - **Accurate Usage:** Only 3 fields actually used in constraints  
  - ProviderCapacity (6 fields) - **Accurate Usage:** Only 4 fields actually used in constraints
  - ProviderPreferences (6 fields) - **All display-only:** No fields used in constraints
  - Consumer (6 fields) - **Accurate Usage:** Only 3 fields actually used in constraints
  - ConsumerPreferences (9 fields) - **Accurate Usage:** Only 1 field actually used in constraints
  - AppointmentData (16 fields) - **Accurate Usage:** Only 8 fields actually used in constraints
  - AppointmentTiming (5 fields) - **Accurate Usage:** Only 2 fields actually used in constraints
  - AppointmentRelationships (5 fields) - **Accurate Usage:** Only 1 field actually used in constraints
  - AppointmentPinning (5 fields) - **All display-only:** No fields used in constraints
- **Key Findings:**
  - **Provider.id:** Used in c001 (provider skill validation) and c007 (preferred_providers check)
  - **Provider.name:** Only used for display/logging, not in any constraints
  - **Provider.availability:** Used in 3 constraints (c002, c004, c008) - corrected from 2
  - **AppointmentData.consumer_id:** Used in c009 (continuity of care) - corrected from display-only
  - **AppointmentData.required_skills:** Used in 2 constraints (c001, c006) - corrected from 1
  - **AppointmentData.duration_min:** Used in 2 constraints (c008, c012) - corrected from 1
  - **AppointmentData.relationships:** Used in c013 (healthcare task sequencing) - corrected from c009
  - **AppointmentRelationships fields:** Used in c013 (care_episode_id, prerequisite_appointment_ids, sequence_order)
  - **Multiple Constraint Usage:** Fields like `provider.role`, `provider.skills`, `provider.capacity` used in 4+ constraints
  - **Display-Only Fields:** Many fields (gray background) are only used for logging, reporting, and display purposes

### Tab 2: Constraints (Direct Implementation Functions)
- **Structure:** Constraint | Function/Test | Description | Example
- **Content:** All 16 constraints (c001-c016) with their implementation functions
- **Coverage:** Assignment constraints (c001-c009) and Scheduling constraints (c010-c016)
- **Details:** Function names, descriptions, and usage examples for each constraint
- **Enhanced:** c007 constraint now includes provider preference checking using provider IDs

### Tab 3: Global Configuration
- **Structure:** Configuration Key | Value | Description | Usage
- **Content:** Global scheduler configuration parameters
- **Categories:** Assignment settings, scheduling settings, optimization parameters
- **Details:** Default values, descriptions, and usage context for each configuration

### Tab 4: Service Configuration & Mapping
- **Structure:** Service | Configuration Key | Value | Description | Mapping
- **Content:** Service-specific configurations for different healthcare services
- **Services:** Behavioral Care, Hospital at Home, PCS, Skilled Nursing
- **Details:** Service-specific settings, constraints, and their mappings to global configurations

## Key Features

1. **Professional Formatting:** Blue headers, borders, auto-adjusted column widths
2. **Accurate Field Usage:** Real analysis of which fields are actually used in constraints
3. **Color Coding:** Gray background for display-only fields
4. **Multiple Constraint Usage:** Clear indication when fields are used in multiple constraints
5. **Cross-References:** Field usage shows which constraints utilize each field
6. **Hierarchical Organization:** Domain model shows clear entity relationships
7. **Practical Examples:** Real-world examples for all fields and configurations
8. **Service-Specific Details:** Different healthcare service configurations documented
9. **Data Integrity:** Provider preferences use UUIDs instead of names for uniqueness

## Recent Updates

### Accurate Field Usage Analysis
- **Provider.id:** Corrected to show used in both c001 (provider skill validation) and c007 (preferred_providers check)
- **Provider.name:** Corrected to show display/logging only (not used in constraints)
- **Provider.availability:** Corrected to show used in 3 constraints (c002, c004, c008)
- **AppointmentData.consumer_id:** Corrected to show used in c009 (continuity of care)
- **AppointmentData.required_skills:** Corrected to show used in 2 constraints (c001, c006)
- **AppointmentData.duration_min:** Corrected to show used in 2 constraints (c008, c012)
- **AppointmentData.relationships:** Corrected to show used in c013 (healthcare task sequencing)
- **AppointmentRelationships fields:** Corrected to show used in c013 (care_episode_id, prerequisite_appointment_ids, sequence_order)
- **Multiple Constraint Usage:** Properly documented fields used in multiple constraints
- **Display-Only Fields:** Identified and color-coded fields only used for logging/display
- **Constraint Accuracy:** Removed incorrect constraint references based on actual code analysis

### Data Integrity Improvements
- **Provider Preferences:** Changed from `List[str]` to `List[UUID]` in `ConsumerPreferences`
- **Benefits:** 
  - Prevents name collisions (multiple providers with same name)
  - Handles name changes (marriage, legal changes)
  - Better database performance (indexed UUID lookups)
  - Guaranteed uniqueness and stability
- **Constraint Enhancement:** c007 constraint updated to include provider preference checking using provider IDs

## Usage

The Excel file serves as a complete reference document for:
- Understanding the domain model structure with accurate field usage
- Identifying which constraints actually use specific fields
- Distinguishing between constraint logic and display/logging fields
- Configuring the system for different healthcare services
- Implementing new features or modifications
- Onboarding new team members

The documentation provides both high-level architectural understanding and detailed implementation guidance for the CAXL Scheduling Engine, with accurate field usage analysis. 

### Technical Notes
- **Break periods**: Now properly implemented in day planning constraints for time slot validation
- **Time off periods**: Implemented in C002 with highest penalty (10 points)
- **Provider availability**: Comprehensive availability checking across assignment and day planning stages 