# Skill Hierarchy Edge Cases

**Purpose**: Test skill matching with various edge cases
**Best for**: Demonstrating robust skill matching
**Complexity**: High

## Edge Cases Covered

### Perfect Match
- Provider has exact skills required
- Multiple providers with same skills
- Skill priority matching

### Skill Hierarchy
- R<PERSON> can do LPN tasks
- LPN can do CNA tasks
- Skill level validation

### No Match
- Required skills not available
- No qualified providers
- Skill gaps in system

### Multiple Skills
- Complex skill combinations
- Skill dependencies
- Cross-skill requirements

### Skill Levels
- Basic vs advanced skills
- Certification levels
- Experience requirements

### Edge Cases
- Empty skills list
- Null provider skills
- Invalid skill format
- Case sensitivity issues
- Duplicate skills
- Non-existent skills

## Data Overview
- **Providers**: 8 (with various skill combinations)
- **Patients**: 12 (with diverse skill requirements)
- **Appointments**: 20 (testing all edge cases)
- **Skill Types**: Basic, Advanced, Specialized, Cross-trained

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/skill_hierarchy/* data/

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Perfect matches should be prioritized
- Skill hierarchy should be respected
- No-match cases should be handled gracefully
- Edge cases should not cause system failures 