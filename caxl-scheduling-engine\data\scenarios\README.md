# Healthcare Scheduling Scenarios

This directory contains various test scenarios for demonstrating different features of the healthcare appointment scheduling system.

## Available Scenarios

### 🟢 Basic Demo
**Path**: `basic_demo/`
**Purpose**: Simple demonstration of core functionality
**Best for**: Initial demos, basic feature overview
**Complexity**: Low
**Features**: Basic assignment, skill matching, geographic areas

### 🟡 Geographic Clustering
**Path**: `geographic_clustering/`
**Purpose**: Demonstrate geographic clustering optimization
**Best for**: Showing how the system groups nearby appointments
**Complexity**: Medium
**Features**: Geographic clustering, service areas, travel optimization

### 🟡 Continuity of Care
**Path**: `continuity_of_care/`
**Purpose**: Demonstrate continuity of care optimization
**Best for**: Showing how the system maintains provider-patient relationships
**Complexity**: Medium
**Features**: Care episodes, provider-patient relationships, continuity

### 🟡 Patient Preferences
**Path**: `patient_preferences/`
**Purpose**: Demonstrate patient preference matching
**Best for**: Showing how the system respects patient preferences
**Complexity**: Medium
**Features**: Language, gender, cultural, provider preferences

### 🔴 Workload Balancing
**Path**: `workload_balancing/`
**Purpose**: Demonstrate workload distribution optimization
**Best for**: Showing fair distribution of work
**Complexity**: Medium
**Features**: Workload balancing, capacity management

### 🔴 Capacity Management
**Path**: `capacity_management/`
**Purpose**: Demonstrate provider capacity constraints
**Best for**: Showing capacity limits and overload prevention
**Complexity**: Medium
**Features**: Capacity thresholds, overload prevention

### 🔴 Route Optimization
**Path**: `route_optimization/`
**Purpose**: Demonstrate advanced route optimization
**Best for**: Showing travel time optimization
**Complexity**: High
**Features**: Route optimization, travel time minimization

### 🔴 Weather Impact
**Path**: `weather_impact/`
**Purpose**: Demonstrate weather-aware scheduling
**Best for**: Showing weather impact on travel times
**Complexity**: Medium
**Features**: Weather integration, travel time adjustments

### 🔴 Rush Hour Traffic
**Path**: `rush_hour_traffic/`
**Purpose**: Demonstrate traffic-aware scheduling
**Best for**: Showing traffic impact on scheduling
**Complexity**: Medium
**Features**: Traffic patterns, time-based adjustments

## Usage Instructions

### 1. Select a Scenario
Choose the scenario that best demonstrates the feature you want to showcase.

### 2. Copy Scenario Data
```bash
# Copy scenario data to main data folder
cp -r data/scenarios/[scenario_name]/* data/
```

### 3. Configure Features
Edit `config/scheduler.yml` to enable/disable relevant features:
```yaml
# Enable features for the scenario
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_patient_preferences: true
enable_workload_balancing: true
enable_provider_capacity_management: true
enable_route_optimization: true
```

### 4. Run Jobs
```bash
# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job
python -m src.appointment_scheduler.jobs.day_plan
```

### 5. Analyze Results
Check the logs and output to see how the system handled the scenario.

## Scenario Selection Guide

| Demo Type | Recommended Scenarios |
|-----------|----------------------|
| **Basic Demo** | `basic_demo` |
| **Geographic Features** | `geographic_clustering` |
| **Care Quality** | `continuity_of_care` |
| **Patient Satisfaction** | `patient_preferences` |
| **Operational Efficiency** | `workload_balancing`, `capacity_management` |
| **Advanced Features** | `route_optimization`, `weather_impact`, `rush_hour_traffic` |

## Feature Toggle Examples

### Basic Plan Demo
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: false
enable_provider_capacity_management: false
enable_route_optimization: false
```

### Premium Plan Demo
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: false
```

### Enterprise Plan Demo
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: true
enable_advanced_traffic_integration: true
```

## Tips for Demos

1. **Start Simple**: Begin with `basic_demo` for new audiences
2. **Build Complexity**: Progress to more advanced scenarios
3. **Show Toggles**: Demonstrate feature enable/disable
4. **Compare Results**: Show before/after with different settings
5. **Explain Constraints**: Highlight hard vs soft constraints
6. **Show Logs**: Demonstrate detailed logging and metrics 