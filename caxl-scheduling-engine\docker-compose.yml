services:
  # FastAPI Web Server
  caxl_api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - HOST=0.0.0.0
      - PORT=8080
      - PROJECT_NAME=caxl-scheduling-engine
      - API_V1_STR=/api/v1
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=True
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      - CORS_EXPOSE_HEADERS=["*"]
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    command: uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
    networks:
      - caxl_network
    depends_on:
      - caxl_scheduler

  # Scheduler Daemon Service
  caxl_scheduler:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    command: python -m caxl_scheduling_engine.jobs.scheduler --mode daemon --config-folder config
    networks:
      - caxl_network
    restart: unless-stopped

networks:
  caxl_network:
    driver: bridge
