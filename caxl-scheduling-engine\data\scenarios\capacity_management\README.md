# Capacity Management Scenario

**Purpose**: Demonstrate provider capacity constraints and overload prevention
**Best for**: Showing capacity limits and overload prevention
**Complexity**: Medium

## Features Demonstrated
- Provider capacity management
- Overload prevention
- Capacity thresholds
- Dynamic capacity adjustments
- Seasonal capacity variations

## Data Overview
- **Providers**: 4 (with varying capacity limits)
- **Patients**: 10 (with diverse appointment needs)
- **Appointments**: 15 (designed to test capacity limits)
- **Capacity Types**: Daily hours, task counts, skill-specific limits

## Capacity Scenarios
1. **Optimal Capacity**: Providers working at ideal capacity
2. **Under Capacity**: Providers with available capacity
3. **Over Capacity**: Providers exceeding their limits
4. **Dynamic Capacity**: Capacity that changes based on conditions

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/capacity_management/* data/

# Enable capacity management
# Edit config/scheduler.yml: enable_provider_capacity_management: true

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- Providers should not exceed their capacity limits
- Workload should be distributed to prevent overload
- Capacity constraints should be respected
- System should handle capacity violations gracefully 