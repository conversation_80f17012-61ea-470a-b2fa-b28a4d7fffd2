# Provider Unavailability Scenarios

**Purpose**: Test system behavior when providers become unavailable, requiring unpinning and replanning
**Best for**: Testing dynamic rescheduling and constraint handling
**Complexity**: High

## Features Demonstrated
- Provider becomes unavailable after initial assignment
- Automatic unpinning of affected appointments
- Replanning and reassignment to available providers
- Handling of urgent vs non-urgent appointments during disruption
- Cascade effects of provider unavailability

## Data Overview
- **Providers**: 4 (with one becoming unavailable)
- **Patients**: 8 (various priority levels)
- **Appointments**: 12 (some initially assigned to unavailable provider)
- **Geographic Coverage**: New York Metro Area

## Test Dimensions Covered
- **availability**: provider_unavailability, emergency_rescheduling
- **provider_assignment**: provider_changes, reassignment
- **capacity**: dynamic_capacity, emergency_handling
- **punctuality**: schedule_disruption, replanning

## Special Test Cases
1. **Provider Sudden Unavailability**: Provider becomes sick/unavailable
2. **Unpinning Required**: Previously assigned appointments must be unpinned
3. **Urgent Appointment Handling**: High priority appointments need immediate reassignment
4. **Cascade Replanning**: Other appointments may need to shift to accommodate changes

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/provider_unavailability/* data/

# Run initial assignment
python -m appointment_scheduler.jobs.assign_appointments

# Simulate provider unavailability (modify provider status)
# Then run reassignment
python -m appointment_scheduler.jobs.assign_appointments
```

## Expected Outcomes
- System should detect provider unavailability
- Affected appointments should be unpinned automatically
- Alternative providers should be found when possible
- Some appointments may remain unassigned if no alternatives exist
