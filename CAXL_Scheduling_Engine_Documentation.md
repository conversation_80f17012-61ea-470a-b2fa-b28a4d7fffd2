# CAXL Scheduling Engine - Comprehensive Documentation

## Table of Contents
1. [What is Timefold?](#what-is-timefold)
2. [Timefold Core Concepts](#timefold-core-concepts)
3. [Simple Timefold Example](#simple-timefold-example)
4. [Project Architecture](#project-architecture)
5. [Configuration System](#configuration-system)
6. [Constraint System](#constraint-system)
7. [Two-Job System](#two-job-system)
8. [How It All Works Together](#how-it-all-works-together)

---

## What is Timefold?

Timefold is a constraint satisfaction and optimization solver that helps solve complex scheduling and planning problems. It uses AI-powered algorithms to find optimal solutions while respecting business rules (constraints).

**Key Benefits:**
- Handles complex scheduling with multiple constraints
- Finds optimal solutions automatically
- Scales to handle large datasets
- Provides real-time optimization

---

## Timefold Core Concepts

### Planning Entity
Objects that need to be assigned/scheduled. These have **Planning Variables** that the solver will optimize.

### Planning Variable
Properties of Planning Entities that the solver can change to find optimal solutions.

### Planning Solution
The complete problem definition containing all entities, facts, and the solution score.

### Problem Facts
Static data that doesn't change during solving (providers, time slots, locations).

### Value Range Provider
Defines possible values for Planning Variables.

### Planning Score
Measures solution quality using Hard/Soft constraints:
- **Hard constraints**: Must be satisfied (e.g., provider availability)
- **Soft constraints**: Preferences to optimize (e.g., minimize travel time)

---

## Simple Timefold Example

```python
# Planning Entity - What needs to be scheduled
@planning_entity
@dataclass
class AppointmentAssignment:
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = None
    assigned_date: Annotated[Optional[date], PlanningVariable] = None

# Planning Solution - Complete problem definition
@planning_solution
@dataclass
class AppointmentSchedule:
    providers: Annotated[List[Provider], ValueRangeProvider]
    available_dates: Annotated[List[date], ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = None
```

**How it works:**
1. Solver tries different combinations of `provider` and `assigned_date` for each appointment
2. Constraints evaluate each combination and assign scores
3. Solver finds the combination with the best overall score

---

## Project Architecture

```
CAXL Scheduling Engine Architecture

┌─────────────────────────────────────────────────────────────────┐
│                    CAXL Scheduling Engine                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  Scheduler.py   │    │ Config Manager  │                    │
│  │   (Orchestrator)│◄──►│                 │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │ Job 1: Assign   │    │ Job 2: DayPlan  │                    │
│  │ Appointments    │    │                 │                    │
│  │ (2:00 AM)       │    │ (6:00 AM)       │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Assignment       │    │Day Planning     │                    │
│  │Constraints      │    │Constraints      │                    │
│  │(9 constraints)  │    │(7 constraints)  │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Timefold Solver  │    │Timefold Solver  │                    │
│  │(Assignment)     │    │(Time Slots)     │                    │
│  └─────────────────┘    └─────────────────┘                    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

Data Flow:
1. Unassigned Appointments → Assignment Job → Appointments with Provider+Date
2. Scheduled Appointments → DayPlan Job → Appointments with Time Slots
```

### Core Components

#### 1. Domain Models (`domain.py`)
Framework-agnostic business entities:
- `AppointmentData`: Core appointment information
- `Provider`: Healthcare providers with skills, availability, capacity
- `Consumer`: Patients/clients requiring services
- `Location`: Geographic locations with coordinates

#### 2. Planning Models (`planning_models.py`)
Timefold-specific wrappers:
- `AppointmentAssignment`: Links appointments to providers and dates
- `TimeSlotAssignment`: Links scheduled appointments to time slots
- `AppointmentSchedule`: Complete assignment solution
- `DaySchedule`: Complete day planning solution

---

## Configuration System

The system uses a **three-level configuration approach** for maximum flexibility and business rule management:

### Three-Level Configuration Hierarchy

```
┌─────────────────────────────────────────────────────────────────┐
│                    THREE-LEVEL CONFIGURATION                    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   GLOBAL CONFIG │    │  SERVICE CONFIG │                    │
│  │  (scheduler.yml)│    │ (service.yml)   │                    │
│  │                 │    │                 │                    │
│  │ • Federal       │    │ • State         │                    │
│  │   Holidays      │    │   Holidays      │                    │
│  │ • Organization  │    │ • Service-      │                    │
│  │   Blackouts     │    │   Specific      │                    │
│  │ • Default       │    │   Rules         │                    │
│  │   Policies      │    │ • Weekend       │                    │
│  └─────────────────┘    │   Coverage      │                    │
│           │              └─────────────────┘                    │
│           │                       │                             │
│           ▼                       ▼                             │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              MERGED CONFIGURATION                           │ │
│  │  (Global + Service + Provider Overrides)                   │ │
│  └─────────────────────────────────────────────────────────────┘ │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   PROVIDER      │    │   CONSTRAINT    │                    │
│  │   OVERRIDES     │    │   EXECUTION     │                    │
│  │                 │    │                 │                    │
│  │ • Vacation      │    │ • C002 uses     │                    │
│  │   Days          │    │   merged config │                    │
│  │ • Sick Leave    │    │ • No hardcoded  │                    │
│  │ • Personal      │    │   values        │                    │
│  │   Time Off      │    │ • Flexible      │                    │
│  └─────────────────┘    │   business      │                    │
│                         │   rules         │                    │
│                         └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
```

### Level 1: Global Configuration (`scheduler.yml`)

Organization-wide settings that apply to all services and providers:

```yaml
# Global availability configuration
global_availability:
  # Organization-wide blackout periods
  blackout_periods:
    - name: "Christmas Break"
      start_date: "12-24"
      end_date: "12-26"
      reason: "organization_holiday"
      severity: "hard"
    
    - name: "New Year Break"
      start_date: "12-31"
      end_date: "01-02"
      reason: "organization_holiday"
      severity: "hard"
  
  # Organization-wide holidays (US Federal Holidays)
  holidays:
    - name: "New Year's Day"
      date: "01-01"
      type: "federal"
    
    - name: "Independence Day"
      date: "07-04"
      type: "federal"
    
    - name: "Christmas Day"
      date: "12-25"
      type: "federal"
  
  # Default weekend coverage policy
  default_weekend_coverage: false
  weekend_coverage_penalty: 2

# System-wide settings
rolling_window_days: 7
max_solving_time_seconds: 300
log_level: "INFO"

# Feature Toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_route_optimization: true
```

### Level 2: Service-Specific Configuration

Each healthcare service has its own configuration file with service-specific availability rules:

**Example: `hospital_at_home.yml` (24/7 Service)**
```yaml
service_type: hospital_at_home
required_skills:
  - "Registered Nurse (RN)"
  - "Critical Care Nursing"

# Service-specific availability settings
service_availability:
  # Service-specific blackout periods
  blackout_periods:
    - name: "Hospital at Home Training"
      start_date: "06-15"
      end_date: "06-17"
      reason: "staff_training"
      severity: "soft"
  
  # Service-specific holidays (state-specific examples)
  holidays:
    - name: "Texas Independence Day"
      date: "03-02"
      type: "state"
      state: "TX"
    
    - name: "San Jacinto Day"
      date: "04-21"
      type: "state"
      state: "TX"
    
    - name: "Cesar Chavez Day"
      date: "03-31"
      type: "state"
      state: "CA"
  
  # Service-specific weekend coverage (24/7 service)
  weekend_coverage:
    enabled: true
    saturday_coverage: true
    sunday_coverage: true
    weekend_penalty: 1
```

**Example: `behavioral_care.yml` (Monday-Friday Service)**
```yaml
service_type: behavioral_care
required_skills:
  - "Licensed Clinical Social Worker (LCSW)"
  - "Crisis Intervention"

# Service-specific availability settings
service_availability:
  # Service-specific blackout periods
  blackout_periods:
    - name: "Behavioral Health Conference"
      start_date: "05-20"
      end_date: "05-22"
      reason: "professional_development"
      severity: "soft"
  
  # Service-specific holidays (no additional state holidays)
  holidays: []
  
  # Service-specific weekend coverage (Monday-Friday only)
  weekend_coverage:
    enabled: false
    saturday_coverage: false
    sunday_coverage: false
    weekend_penalty: 5
```

### Level 3: Provider-Specific Overrides

Provider-level availability settings that override global and service configurations:

```python
# Provider availability with personal overrides
ProviderAvailability(
    # Provider-specific time off periods
    time_off_periods=[
        (date(2024, 8, 1), date(2024, 8, 10)),  # Vacation
        (date(2024, 12, 24), date(2024, 12, 26))  # Personal holiday
    ],
    
    # Provider-specific working days
    working_days=["monday", "tuesday", "wednesday", "thursday", "friday"],
    
    # Provider-specific break periods
    break_periods=[(time(12, 0), time(13, 0))],  # Lunch break
    
    # Date-specific availability overrides
    date_specific_availability=[
        DateSpecificProviderAvailability(
            date=date(2024, 6, 15),
            available_start=time(10, 0),
            available_end=time(16, 0),
            reason="half_day_leave"
        )
    ]
)
```

### Configuration Merging Logic

The system merges configurations in the following order (highest priority first):

1. **Provider Level**: Personal time off, working days, break periods
2. **Service Level**: State holidays, service-specific blackouts, weekend coverage
3. **Global Level**: Federal holidays, organization-wide blackouts, default policies

**Example: Texas Hospital at Home Provider on April 21, 2024**

```python
# Configuration merging for a Texas provider
global_config = {
    "holidays": ["New Year's Day", "Independence Day", "Christmas Day"],
    "blackout_periods": ["Christmas Break", "New Year Break"],
    "default_weekend_coverage": false
}

service_config = {
    "holidays": ["Texas Independence Day", "San Jacinto Day"],  # April 21
    "blackout_periods": ["Hospital Training"],
    "weekend_coverage": {"enabled": true, "saturday_coverage": true}
}

provider_config = {
    "time_off_periods": [],
    "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
}

# Result: Provider UNAVAILABLE on April 21 (San Jacinto Day)
# Even though it's a Saturday and service allows weekend coverage,
# the state holiday takes precedence
```

### Configuration Manager (`config_manager.py`)

```python
class ConfigManager:
    def get_scheduler_config() -> SchedulerConfig
    def get_service_config(service_type: str) -> ServiceConfig
    def validate_configuration() -> Dict[str, Any]
    def reload_configuration() -> Dict[str, Any]

class AvailabilityService:
    def get_blackout_periods(service_type: str, state: str) -> List[BlackoutPeriod]
    def get_holidays(service_type: str, state: str) -> List[Holiday]
    def get_weekend_coverage(service_type: str) -> Dict[str, Any]
    def is_blackout_period(date: date, service_type: str, state: str) -> bool
    def is_holiday(date: date, service_type: str, state: str) -> bool
    def has_weekend_coverage(date: date, service_type: str) -> bool
```

**Key Features:**
- **Three-level hierarchy**: Global → Service → Provider
- **State-specific rules**: Different holidays for different states
- **Service-specific policies**: Different weekend coverage for different services
- **Provider overrides**: Personal time off and preferences
- **Automatic merging**: Configuration automatically merged at runtime
- **Fallback support**: Hardcoded fallbacks if configuration fails
- **Hot-reload capability**: Configuration changes without restart
- **Validation**: Automatic validation of configuration structure

### Business Benefits

1. **Multi-State Compliance**: Different states can have different holidays
2. **Service Flexibility**: 24/7 services vs. Monday-Friday services
3. **Provider Autonomy**: Personal time off and preferences
4. **Easy Maintenance**: No code changes for business rule updates
5. **Scalability**: New services and states added through configuration
6. **Compliance**: Easy to implement state-specific labor laws
7. **Audit Trail**: Clear configuration hierarchy for compliance reporting

---

## Constraint System

Constraints define business rules and optimization preferences. The system has two sets of constraints:

### Assignment Constraints (9 constraints)
Applied during provider/date assignment:

```
Hard Constraints (Must be satisfied):
├── C001: Provider Skill Validation
├── C002: Date-based Availability  
└── C004: Provider Role Match

Soft Constraints (Optimization preferences):
├── C003: Geographic Service Area
├── C005: Workload Balance Optimization
├── C006: Geographic Clustering
├── C007: Patient Preference Matching
├── C008: Provider Capacity Management
└── C009: Continuity of Care
```

### Day Planning Constraints (7 constraints)
Applied during time slot assignment:

```
Hard Constraints:
├── C010: Time Slot Availability
├── C011: Appointment Overlap Prevention
└── C012: Appointment Duration Fit

Soft Constraints:
├── C012: Preferred Hours Optimization
├── C013: Healthcare Task Sequencing
├── C014: Travel Time Optimization
├── C015: Break Time Management
└── C016: Route Optimization
```

### Understanding Constraints: Hard vs Soft

Constraints are the business rules that guide the Timefold solver to find optimal solutions. They come in two types:

#### Hard Constraints (Must be satisfied)
Hard constraints represent **mandatory business rules** that cannot be violated. If a solution violates any hard constraint, it gets a **negative hard score** and is considered infeasible.

**Example: Provider Skill Validation (C001)**

````python
def required_skills(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider must have all required skills for the appointment."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and
                                      not _has_required_skills(assignment.provider,
                                                             assignment.appointment_data)))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint("Required skills"))
````

**How it works:**
- **Penalty**: `HardSoftScore.ONE_HARD` = -1 hard score per violation
- **Trigger**: When a provider lacks required skills for an appointment
- **Effect**: Makes the solution infeasible, forcing the solver to find alternatives
- **Business Rule**: "A behavioral health appointment cannot be assigned to a provider without behavioral health certification"

#### Soft Constraints (Optimization preferences)
Soft constraints represent **preferences and optimization goals**. Violations result in **negative soft scores** but don't make solutions infeasible. The solver tries to minimize violations while satisfying all hard constraints.

**Example: Geographic Clustering (C006)**

````python
def geographic_clustering(constraint_factory: ConstraintFactory) -> Constraint:
    """Reward appointments that are geographically clustered."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .join(AppointmentAssignment,
                  Joiners.equal(lambda assignment: assignment.provider),
                  Joiners.equal(lambda assignment: assignment.assigned_date))
            .filter(lambda assignment1, assignment2:
                   _are_geographically_close(assignment1, assignment2))
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment1, assignment2: _calculate_clustering_bonus(assignment1, assignment2))
            .as_constraint("Geographic clustering"))
````

**How it works:**
- **Reward**: `HardSoftScore.ONE_SOFT` = +1 soft score per clustered pair
- **Trigger**: When two appointments for the same provider on the same date are geographically close
- **Effect**: Encourages the solver to group nearby appointments together
- **Business Rule**: "Prefer to schedule appointments close to each other to minimize travel time"

### Penalty and Reward System

#### Penalty System
```python
# Hard penalty - makes solution infeasible
.penalize(HardSoftScore.ONE_HARD)  # -1 hard score

# Soft penalty - reduces solution quality
.penalize(HardSoftScore.ONE_SOFT)  # -1 soft score

# Dynamic penalty based on severity
.penalize(HardSoftScore.ONE_SOFT,
         lambda assignment: calculate_distance_penalty(assignment))
```

#### Reward System
```python
# Fixed reward
.reward(HardSoftScore.ONE_SOFT)  # +1 soft score

# Dynamic reward based on benefit
.reward(HardSoftScore.ONE_SOFT,
        lambda assignment1, assignment2: calculate_clustering_bonus(assignment1, assignment2))
```

#### Score Interpretation
```
Final Score Examples:
- 0hard/-150soft: Feasible solution with 150 soft violations
- -2hard/-50soft: Infeasible solution (2 hard constraint violations)
- 0hard/+25soft: Excellent solution with 25 bonus points

Solver Priority:
1. Eliminate all hard constraint violations (achieve 0hard score)
2. Maximize soft score (minimize penalties, maximize rewards)
```

### Adding a New Constraint: Step-by-Step Guide

Let's add a new constraint: **"Prefer morning appointments for elderly patients"**

#### Step 1: Create the Constraint File
Create `c017_asgn_elderly_morning_preference.py`:

```python
"""
Elderly Morning Preference Constraint (C017)
Soft constraint that rewards morning time slots for elderly patients.
"""

from datetime import time
from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint
from caxl_scheduling_engine.model.planning_models import TimeSlotAssignment

def elderly_morning_preference(constraint_factory: ConstraintFactory) -> Constraint:
    """Reward morning appointments for elderly patients (65+)."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: (
                assignment.time_slot is not None and
                assignment.scheduled_appointment.appointment_data.patient_age >= 65 and
                assignment.time_slot <= time(12, 0)  # Before noon
            ))
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: _calculate_morning_bonus(assignment))
            .as_constraint("Elderly morning preference"))

def _calculate_morning_bonus(assignment: TimeSlotAssignment) -> int:
    """Calculate bonus points for morning appointments."""
    if assignment.time_slot <= time(9, 0):
        return 20  # Early morning bonus
    elif assignment.time_slot <= time(11, 0):
        return 10  # Late morning bonus
    return 5  # Just before noon
```

#### Step 2: Register in Constraint Provider
Add to `day_constraints.py`:

```python
# Import the new constraint
from .c017_asgn_elderly_morning_preference import elderly_morning_preference

@constraint_provider
def define_day_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the day planning stage."""
    config_manager = ConfigManager()
    scheduler_config = config_manager.get_scheduler_config()

    constraints = []

    # Existing constraints...

    # New constraint (conditionally enabled)
    if scheduler_config.enable_elderly_care_optimization:
        constraints.append(elderly_morning_preference(constraint_factory))

    return constraints
```

#### Step 3: Add Feature Toggle
Update `domain.py` SchedulerConfig:

```python
class SchedulerConfig(BaseModel):
    # Existing toggles...

    # New feature toggle
    enable_elderly_care_optimization: bool = False
```

#### Step 4: Update Configuration File
Add to `scheduler.yml`:

```yaml
# Existing toggles...

# Elderly Care Optimization
enable_elderly_care_optimization: true
```

#### Step 5: Add Service-Specific Configuration
Update service config files (e.g., `behavioral_care.yml`):

```yaml
# Existing settings...

# Elderly care settings
elderly_morning_preference_weight: 0.8
morning_cutoff_time: "12:00"
early_morning_bonus: 20
late_morning_bonus: 10
```

#### Step 6: Update Domain Model (if needed)
If the constraint needs new data fields, update `domain.py`:

```python
@dataclass
class AppointmentData:
    # Existing fields...

    # New field for elderly preference constraint
    patient_age: Optional[int] = None
```

#### Step 7: Write Tests
Create `test_c017_elderly_morning_preference.py`:

```python
def test_elderly_morning_preference_reward():
    """Test that elderly patients get bonus for morning appointments."""
    # Setup test data
    elderly_assignment = create_elderly_morning_assignment()

    # Test constraint
    constraint_verifier = ConstraintVerifier.build(
        define_day_constraints, DaySchedule, TimeSlotAssignment
    )

    constraint_verifier.verify_that(elderly_morning_preference)\
        .given(elderly_assignment)\
        .rewards(20)  # Early morning bonus
```

#### Summary of Changes Required:
1. **New constraint file** - Core constraint logic
2. **Constraint provider update** - Register the constraint
3. **Configuration updates** - Add feature toggle and parameters
4. **Domain model updates** - Add required data fields (if needed)
5. **Tests** - Verify constraint behavior
6. **Documentation** - Update constraint list and descriptions

This modular approach ensures that new constraints can be added without affecting existing functionality, and they can be easily enabled/disabled through configuration.

### Feature Toggle Integration
```python
@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    config_manager = ConfigManager()
    scheduler_config = config_manager.get_scheduler_config()
    
    constraints = []
    
    # Always enabled hard constraints
    constraints.append(required_skills(constraint_factory))
    constraints.append(provider_availability(constraint_factory))
    
    # Conditionally enabled soft constraints
    if scheduler_config.enable_workload_balancing:
        constraints.append(workload_balancing(constraint_factory))
    
    if scheduler_config.enable_geographic_clustering:
        constraints.append(geographic_clustering(constraint_factory))
    
    return constraints
```

---

## Two-Job System

The system uses a realistic two-stage approach that mirrors real healthcare scheduling workflows:

### Job 1: Assignment Job (`assign_appointments.py`)
**Schedule:** Nightly at 2:00 AM
**Purpose:** Strategic planning for the upcoming week
**Input:** New appointment requests
**Output:** Appointments with provider and date assigned (no specific time)

```
Assignment Job Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Unassigned      │    │ Timefold        │    │ Assigned        │
│ Appointments    │───►│ Assignment      │───►│ Appointments    │
│                 │    │ Solver          │    │ (Provider+Date) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│• Appointment ID │    │• Skills Match   │    │• Provider: John │
│• Service Type   │    │• Availability   │    │• Date: 2024-01-15│
│• Patient Info   │    │• Geographic     │    │• Time: TBD      │
│• Location       │    │• Workload       │    │• Location: A    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Key Features:**
- Processes batches of appointments efficiently
- Considers provider skills, availability, and geographic constraints
- Balances workload across providers
- Maintains continuity of care relationships

### Job 2: Day Plan Job (`day_plan.py`)
**Schedule:** Daily at 6:00 AM
**Purpose:** Time slot assignment and route optimization for the day
**Input:** Appointments scheduled for today (with provider and date)
**Output:** Appointments with specific time slots assigned

```
Day Plan Job Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Scheduled       │    │ Timefold        │    │ Fully Scheduled │
│ Appointments    │───►│ Day Planning    │───►│ Appointments    │
│ (Provider+Date) │    │ Solver          │    │ (Time Slots)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│• Provider: John │    │• Time Slots     │    │• Provider: John │
│• Date: Today    │    │• Travel Time    │    │• Date: Today    │
│• Time: TBD      │    │• Route Optimize │    │• Time: 10:30 AM │
│• Location: A    │    │• No Conflicts   │    │• Duration: 45min│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Key Features:**
- Optimizes daily schedules with specific time slots
- Minimizes travel time between appointments
- Prevents double-booking and scheduling conflicts
- Considers provider break times and preferences

---

## How It All Works Together

### System Workflow

```
Complete Scheduling Workflow:

Day 1 (2:00 AM) - Assignment Job
┌─────────────────────────────────────────────────────────────────┐
│ 1. Load unassigned appointments                                 │
│ 2. Load available providers and their constraints               │
│ 3. Apply assignment constraints (skills, availability, etc.)    │
│ 4. Solve optimization problem                                   │
│ 5. Output: Appointments with Provider + Date (no time)         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
Day 1 (6:00 AM) - Day Plan Job
┌─────────────────────────────────────────────────────────────────┐
│ 1. Load today's scheduled appointments                          │
│ 2. Generate available time slots for each provider             │
│ 3. Apply day planning constraints (time, travel, conflicts)    │
│ 4. Solve time slot optimization problem                        │
│ 5. Output: Fully scheduled appointments with time slots        │
└─────────────────────────────────────────────────────────────────┘
```

### Configuration Integration

```
Configuration Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ scheduler.yml   │    │ Config Manager  │    │ Feature Toggles │
│ (Global Config) │───►│                 │───►│ Enable/Disable  │
└─────────────────┘    │                 │    │ Constraints     │
                       │                 │    └─────────────────┘
┌─────────────────┐    │                 │    ┌─────────────────┐
│ service.yml     │───►│                 │───►│ Service-Specific│
│ (Service Config)│    │                 │    │ Parameters      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Scheduler Orchestration (`scheduler.py`)

The main scheduler coordinates both jobs:

```python
class AppointmentScheduler:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()

    def setup_schedule(self):
        # Assignment job runs nightly at 2 AM
        schedule.every().day.at("02:00").do(self.run_assign_appointments)

        # Day plan job runs daily at 6 AM
        schedule.every().day.at("06:00").do(self.run_today_day_plan)

    def run_daemon(self):
        while self._running:
            schedule.run_pending()
            time.sleep(60)
```

### Key Benefits of This Architecture

1. **Separation of Concerns**: Assignment and time planning are handled separately
2. **Realistic Workflow**: Mirrors how healthcare scheduling actually works
3. **Scalability**: Each job can be optimized independently
4. **Flexibility**: Feature toggles allow customization per deployment
5. **Maintainability**: Clear separation between configuration, constraints, and logic

### Example End-to-End Flow

```
Monday 2:00 AM - Assignment Job runs:
├── Input: 50 new appointment requests for the week
├── Process: Assign providers and dates using assignment constraints
└── Output: 45 appointments assigned, 5 unassigned (no available providers)

Monday 6:00 AM - Day Plan Job runs:
├── Input: 8 appointments scheduled for Monday
├── Process: Assign time slots using day planning constraints
└── Output: 8 appointments with specific times (9:00 AM, 10:30 AM, etc.)

Tuesday 6:00 AM - Day Plan Job runs:
├── Input: 12 appointments scheduled for Tuesday
├── Process: Optimize time slots and travel routes
└── Output: 12 appointments with optimized schedule
```

This architecture ensures that the CAXL Scheduling Engine can handle complex healthcare scheduling requirements while remaining flexible and maintainable.

---

## Running the Scheduler

The CAXL Scheduling Engine provides multiple ways to run the scheduling jobs, offering flexibility for different deployment scenarios.

### Command Line Interface

The main entry point is through the scheduler module:

```bash
python -m caxl_scheduling_engine.jobs.scheduler [OPTIONS]
```

### Execution Modes

#### 1. Daemon Mode (Production)
Runs continuously with scheduled job execution:

```bash
# Start the scheduler daemon
python -m caxl_scheduling_engine.jobs.scheduler --mode daemon

# With custom config folder
python -m caxl_scheduling_engine.jobs.scheduler --mode daemon --config-folder /path/to/config
```

**Daemon Schedule:**
- **Assignment Job**: Runs daily at 2:00 AM
- **Day Plan Job**: Runs daily at 6:00 AM

**Output:**
```
Starting scheduler daemon...
Schedule setup complete:
  - AssignAppointment job: Daily at 02:00
  - DayPlan job: Daily at 06:00
Press Ctrl+C to stop
```

#### 2. Once Mode (Testing/Manual)
Runs a single job immediately:

```bash
# Run assignment job once
python -m caxl_scheduling_engine.jobs.scheduler --mode once --job assign

# Run day plan job once for today
python -m caxl_scheduling_engine.jobs.scheduler --mode once --job dayplan

# Run day plan job for specific date
python -m caxl_scheduling_engine.jobs.scheduler --mode once --job dayplan --date 2024-01-15
```

**Output Example:**
```
=== Job Results ===
Job Type: assign
Total: 50
Assigned: 45
Unassigned: 5
Processing Time: 12.34s
```

### Direct Job Execution

Each job can also be executed directly for development and testing:

#### Assignment Job
```bash
# Direct execution
python -m caxl_scheduling_engine.jobs.assign_appointments

# With custom config
python -m caxl_scheduling_engine.jobs.assign_appointments --config-folder config/
```

#### Day Plan Job
```bash
# Direct execution for today
python -m caxl_scheduling_engine.jobs.day_plan

# For specific date
python -m caxl_scheduling_engine.jobs.day_plan --date 2024-01-15

# With custom config
python -m caxl_scheduling_engine.jobs.day_plan --config-folder config/ --date 2024-01-15
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--mode` | Execution mode: `daemon` or `once` | `daemon` |
| `--job` | Job type: `assign` or `dayplan` (required for once mode) | - |
| `--date` | Target date for dayplan job (YYYY-MM-DD format) | Today |
| `--config-folder` | Path to configuration folder | `config` |

### Usage Examples

#### Development Testing
```bash
# Test assignment job with sample data
python -m caxl_scheduling_engine.jobs.scheduler --mode once --job assign

# Test day planning for tomorrow
python -m caxl_scheduling_engine.jobs.scheduler --mode once --job dayplan --date 2024-01-16
```

#### Production Deployment
```bash
# Start production scheduler
python -m caxl_scheduling_engine.jobs.scheduler --mode daemon --config-folder /etc/caxl/config

# Or using systemd service
systemctl start caxl-scheduler
```

#### Troubleshooting
```bash
# Run with debug logging
LOG_LEVEL=DEBUG python -m caxl_scheduling_engine.jobs.scheduler --mode once --job assign

# Test configuration
python -c "from caxl_scheduling_engine.utils.config_manager import ConfigManager; cm = ConfigManager(); print(cm.validate_configuration())"
```

### Job Execution Summary

The **assign_appointments.py** job can be executed in three ways:

1. **Direct execution**:
   ```bash
   python -m caxl_scheduling_engine.jobs.assign_appointments
   ```

2. **Scheduler once mode**:
   ```bash
   python -m caxl_scheduling_engine.jobs.scheduler --mode once --job assign
   ```

3. **Scheduled daemon mode**:
   ```bash
   python -m caxl_scheduling_engine.jobs.scheduler --mode daemon
   ```
   *(Runs automatically at 2:00 AM daily)*

The **day_plan.py** job can be executed in three ways:

1. **Direct execution**:
   ```bash
   python -m caxl_scheduling_engine.jobs.day_plan
   ```

2. **Scheduler once mode**:
   ```bash
   python -m caxl_scheduling_engine.jobs.scheduler --mode once --job dayplan
   ```

3. **Scheduled daemon mode**:
   ```bash
   python -m caxl_scheduling_engine.jobs.scheduler --mode daemon
   ```
   *(Runs automatically at 6:00 AM daily)*

### Monitoring and Logs

The scheduler creates detailed logs for monitoring:

```
logs/
├── scheduler_2024-01-15.log    # Daily scheduler logs
├── assign_2024-01-15.log       # Assignment job logs
└── dayplan_2024-01-15.log      # Day plan job logs
```

**Log Levels:**
- `INFO`: Normal operation status
- `DEBUG`: Detailed constraint evaluation and solver progress
- `ERROR`: Job failures and configuration issues
- `WARNING`: Suboptimal solutions or constraint violations

This flexible execution model supports both development workflows and production deployments, ensuring the CAXL Scheduling Engine can be easily integrated into existing healthcare IT infrastructure.
