appointments:
  # Zero duration appointment
  - consumer_id: "overlap-consumer-001"
    required_skills: ["basic_care"]
    duration_min: 0
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "10:00"
      strict_timing: false

  # Very long duration appointment
  - consumer_id: "overlap-consumer-002"
    required_skills: ["wound_care"]
    duration_min: 240  # 4 hours
    appointment_date: "2024-01-15"
    priority: "high"
    timing:
      preferred_time: "09:00"
      strict_timing: true

  # Exact same time appointments (should create conflict)
  - consumer_id: "overlap-consumer-003"
    required_skills: ["medication_management"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "14:00"
      strict_timing: true

  - consumer_id: "overlap-consumer-004"
    required_skills: ["medication_management"]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "14:00"  # Same time as above
      strict_timing: true

  # Partially overlapping appointments
  - consumer_id: "overlap-consumer-005"
    required_skills: ["assessment"]
    duration_min: 60
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "15:00"
      strict_timing: false

  - consumer_id: "overlap-consumer-006"
    required_skills: ["iv_therapy"]
    duration_min: 90
    appointment_date: "2024-01-15"
    priority: "high"
    timing:
      preferred_time: "15:30"  # Overlaps with above
      strict_timing: false

  # Adjacent appointments (should work with proper buffer)
  - consumer_id: "overlap-consumer-007"
    required_skills: ["basic_care"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "11:00"
      strict_timing: true

  - consumer_id: "overlap-consumer-008"
    required_skills: ["companionship"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "11:30"  # Adjacent to above
      strict_timing: true

  # Multiple appointments for same consumer (episode continuity)
  - consumer_id: "overlap-consumer-001"
    required_skills: ["medication_management"]
    duration_min: 15
    appointment_date: "2024-01-15"
    priority: "high"
    timing:
      preferred_time: "16:00"
      strict_timing: false

  - consumer_id: "overlap-consumer-001"
    required_skills: ["wound_care"]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "high"
    timing:
      preferred_time: "16:30"
      strict_timing: false

  # Concurrent appointments requiring different skills
  - consumer_id: "overlap-consumer-002"
    required_skills: ["basic_care"]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "low"
    timing:
      preferred_time: "13:00"
      strict_timing: false

  # End of day appointment (might not fit)
  - consumer_id: "overlap-consumer-003"
    required_skills: ["assessment"]
    duration_min: 120
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "17:00"  # Near end of working hours
      strict_timing: false
