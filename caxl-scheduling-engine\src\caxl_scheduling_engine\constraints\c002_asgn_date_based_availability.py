"""
Date-Based Availability Constraint (C002)

This constraint ensures that providers are available during the assigned date.
This is a HARD constraint that must be satisfied for valid assignments.

Enhanced logic includes:
- Available date, Unavailable date, Blackout period, Weekend coverage, Holiday
- Invalid date format, Timezone issues, Leap year, DST changes
- Configuration-based availability using three-level system:
  1. Global level (organization-wide settings)
  2. Service level (service-specific settings)
  3. Provider level (provider-specific overrides)
"""

from datetime import date

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from caxl_scheduling_engine.model.domain import Provider, weekday_from_int
from caxl_scheduling_engine.model.planning_models import AppointmentAssignment
from caxl_scheduling_engine.utils.config_manager import ConfigManager
from caxl_scheduling_engine.utils.availability_service import AvailabilityService


def provider_availability(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider must be available during the assigned date."""
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: assignment.provider is not None and 
                   assignment.assigned_date is not None and
                   not _is_provider_available_enhanced(assignment.provider, assignment.assigned_date))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: _calculate_availability_penalty(assignment.provider, assignment.assigned_date) if assignment.provider is not None and assignment.assigned_date is not None else 1)
            .as_constraint("Provider availability"))


def _is_provider_available_enhanced(provider: Provider, target_date: date) -> bool:
    """
    Enhanced availability check with comprehensive date handling.
    
    Returns:
        bool: True if provider is available, False otherwise
    """
    if provider is None or target_date is None:
        return False
    
    # Check if provider has availability configuration
    if provider.availability is None:
        # If no availability is configured, assume available on weekdays
        weekday = target_date.weekday()
        return weekday < 5  # Monday to Friday
    
    # Check time off periods first (highest priority)
    if _is_time_off_period(provider, target_date):
        return False
    
    # Check basic availability using the new ProviderAvailability model
    shift_hours = provider.availability.get_shift_hours(target_date)
    if shift_hours is None:
        return False  # Provider is not available on this date
    
    # Check working days
    if not _is_working_day_enhanced(provider, target_date):
        return False
    
    # Check blackout periods using configuration
    if _is_blackout_period_config(target_date, provider):
        return False
    
    # Check holidays using configuration
    if _is_holiday_config(target_date, provider):
        return False
    
    # Check weekend coverage using configuration
    if not _has_weekend_coverage_config(provider, target_date):
        return False
    
    return True


def _is_time_off_period(provider: Provider, target_date: date) -> bool:
    """Check if the target date falls within any of the provider's time off periods."""
    if provider.availability is None:
        return False
    
    for start_date, end_date in provider.availability.time_off_periods:
        if start_date <= target_date <= end_date:
            return True
    
    return False


def _is_working_day_enhanced(provider: Provider, target_date: date) -> bool:
    """Check if the target date is a working day for the provider."""
    weekday = target_date.weekday()
    weekday_enum = weekday_from_int(weekday)
    
    # Check if provider has availability configuration
    if provider.availability is not None:
        return weekday_enum in provider.availability.working_days
    
    # Default: Monday-Friday (0=Monday, 6=Sunday)
    return weekday < 5


def _is_blackout_period_config(target_date: date, provider: Provider) -> bool:
    """Check if date falls within blackout periods from configuration."""
    try:
        config_manager = ConfigManager()
        availability_service = AvailabilityService(config_manager)
        
        # Get provider's service type and state
        service_type = _get_provider_service_type(provider)
        state = _get_provider_state(provider)
        
        return availability_service.is_blackout_period(target_date, service_type, state)
    except Exception:
        # Fallback to hardcoded values if configuration fails
        return _is_blackout_period_fallback(target_date)


def _is_holiday_config(target_date: date, provider: Provider) -> bool:
    """Check if date is a holiday from configuration."""
    try:
        config_manager = ConfigManager()
        availability_service = AvailabilityService(config_manager)
        
        # Get provider's service type and state
        service_type = _get_provider_service_type(provider)
        state = _get_provider_state(provider)
        
        return availability_service.is_holiday(target_date, service_type, state)
    except Exception:
        # Fallback to hardcoded values if configuration fails
        return _is_holiday_fallback(target_date)


def _has_weekend_coverage_config(provider: Provider, target_date: date) -> bool:
    """Check if provider has weekend coverage from configuration."""
    weekday = target_date.weekday()
    is_weekend = weekday >= 5  # Saturday (5) or Sunday (6)
    
    if not is_weekend:
        return True  # Not a weekend, so no issue
    
    try:
        config_manager = ConfigManager()
        availability_service = AvailabilityService(config_manager)
        
        # Get provider's service type
        service_type = _get_provider_service_type(provider)
        
        return availability_service.has_weekend_coverage(target_date, service_type)
    except Exception:
        # Fallback to hardcoded values if configuration fails
        return _has_weekend_coverage_fallback(provider, target_date)


def _get_provider_service_type(provider: Provider) -> str:
    """Get the service type for a provider."""
    # This would typically come from provider data or appointment context
    # For now, return a default service type
    return "hospital_at_home"


def _get_provider_state(provider: Provider) -> str:
    """Get the state for a provider."""
    if provider.home_location and provider.home_location.state:
        return provider.home_location.state
    return None


def _is_blackout_period_fallback(target_date: date) -> bool:
    """Fallback blackout period check with hardcoded values."""
    # Example blackout periods (this would come from configuration)
    blackout_periods = [
        # Christmas break
        (date(target_date.year, 12, 24), date(target_date.year, 12, 26)),
        # New Year break
        (date(target_date.year, 12, 31), date(target_date.year + 1, 1, 2)),
        # Summer vacation period (example)
        (date(target_date.year, 7, 1), date(target_date.year, 7, 15)),
    ]
    
    for start_date, end_date in blackout_periods:
        if start_date <= target_date <= end_date:
            return True
    
    return False


def _is_holiday_fallback(target_date: date) -> bool:
    """Fallback holiday check with hardcoded values."""
    # US Federal Holidays (simplified)
    holidays = {
        (1, 1): "New Year's Day",
        (7, 4): "Independence Day",
        (12, 25): "Christmas Day",
        # Add more holidays as needed
    }
    
    return (target_date.month, target_date.day) in holidays


def _has_weekend_coverage_fallback(provider: Provider, target_date: date) -> bool:
    """Fallback weekend coverage check with hardcoded values."""
    weekday = target_date.weekday()
    is_weekend = weekday >= 5  # Saturday (5) or Sunday (6)
    
    if not is_weekend:
        return True  # Not a weekend, so no issue
    
    # For now, assume providers don't work weekends unless specified
    # In a real implementation, this would check provider preferences
    return False


def _calculate_availability_penalty(provider: Provider, target_date: date) -> int:
    """
    Calculate penalty based on availability violation severity.
    
    Returns:
        int: Penalty score (higher = more severe violation)
    """
    if provider is None or target_date is None:
        return 1
    
    penalty = 1  # Base penalty
    
    # Check different types of violations and add penalties
    
    # Time off period violation (highest penalty - provider is on vacation/sick leave)
    if _is_time_off_period(provider, target_date):
        penalty += 10
    
    # Holiday violation (very high penalty)
    if _is_holiday_config(target_date, provider):
        penalty += 5
    
    # Blackout period violation
    if _is_blackout_period_config(target_date, provider):
        penalty += 3
    
    # Weekend violation
    weekday = target_date.weekday()
    if weekday >= 5 and not _has_weekend_coverage_config(provider, target_date):
        penalty += 2
    
    # Non-working day violation
    if not _is_working_day_enhanced(provider, target_date):
        penalty += 1
    
    return penalty 