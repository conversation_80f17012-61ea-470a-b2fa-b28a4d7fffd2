"""
Availability Service for managing three-level configuration system.

This service handles availability configuration across:
1. Global level (organization-wide settings)
2. Service level (service-specific settings)  
3. Provider level (provider-specific overrides)
"""

from datetime import date
from typing import List, Dict, Any, Optional

from caxl_scheduling_engine.model.domain import (
    Holiday, BlackoutPeriod, WeekendCoverage, 
    GlobalAvailability, ServiceAvailability
)
from caxl_scheduling_engine.utils.config_manager import ConfigManager


class AvailabilityService:
    """Manages availability configuration across global, service, and provider levels."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
    
    def _parse_date_string(self, date_str: str, year: int) -> date:
        """Parse MM-DD format date string to date object."""
        month, day = map(int, date_str.split('-'))
        return date(year, month, day)
    
    def get_blackout_periods(self, service_type: str, state: Optional[str] = None) -> List[BlackoutPeriod]:
        """Get merged blackout periods (global + service)."""
        global_config = self.config_manager.get_scheduler_config()
        service_config = self.config_manager.get_service_config(service_type)
        
        blackout_periods = []
        
        # Add global blackout periods
        global_periods = global_config.global_availability.blackout_periods
        if state:
            # Filter by state if specified
            state_periods = [p for p in global_periods if p.state == state or p.state is None]
            blackout_periods.extend(state_periods)
        else:
            blackout_periods.extend(global_periods)
        
        # Add service-specific blackout periods
        if service_config and service_config.service_availability:
            service_periods = service_config.service_availability.blackout_periods
            if state:
                # Filter by state if specified
                state_periods = [p for p in service_periods if p.state == state or p.state is None]
                blackout_periods.extend(state_periods)
            else:
                blackout_periods.extend(service_periods)
        
        return blackout_periods
    
    def get_holidays(self, service_type: str, state: Optional[str] = None) -> List[Holiday]:
        """Get merged holidays (global + service)."""
        global_config = self.config_manager.get_scheduler_config()
        service_config = self.config_manager.get_service_config(service_type)
        
        holidays = []
        
        # Add global holidays
        global_holidays = global_config.global_availability.holidays
        if state:
            # Filter by state if specified
            state_holidays = [h for h in global_holidays if h.state == state or h.state is None]
            holidays.extend(state_holidays)
        else:
            holidays.extend(global_holidays)
        
        # Add service-specific holidays
        if service_config and service_config.service_availability:
            service_holidays = service_config.service_availability.holidays
            if state:
                # Filter by state if specified
                state_holidays = [h for h in service_holidays if h.state == state or h.state is None]
                holidays.extend(state_holidays)
            else:
                holidays.extend(service_holidays)
        
        return holidays
    
    def get_weekend_coverage(self, service_type: str) -> Dict[str, Any]:
        """Get weekend coverage settings (global + service)."""
        global_config = self.config_manager.get_scheduler_config()
        service_config = self.config_manager.get_service_config(service_type)
        
        # Default to global settings
        coverage = {
            "enabled": global_config.global_availability.default_weekend_coverage,
            "saturday_coverage": False,
            "sunday_coverage": False,
            "weekend_penalty": global_config.global_availability.weekend_coverage_penalty
        }
        
        # Override with service-specific settings
        if service_config and service_config.service_availability.weekend_coverage:
            service_coverage = service_config.service_availability.weekend_coverage
            coverage.update({
                "enabled": service_coverage.enabled,
                "saturday_coverage": service_coverage.saturday_coverage,
                "sunday_coverage": service_coverage.sunday_coverage,
                "weekend_penalty": service_coverage.weekend_penalty
            })
        
        return coverage
    
    def is_blackout_period(self, target_date: date, service_type: str, state: Optional[str] = None) -> bool:
        """Check if date falls within blackout periods from configuration."""
        blackout_periods = self.get_blackout_periods(service_type, state)
        
        for period in blackout_periods:
            start_date = self._parse_date_string(period.start_date, target_date.year)
            end_date = self._parse_date_string(period.end_date, target_date.year)
            
            if start_date <= target_date <= end_date:
                return True
        
        return False
    
    def is_holiday(self, target_date: date, service_type: str, state: Optional[str] = None) -> bool:
        """Check if date is a holiday from configuration."""
        holidays = self.get_holidays(service_type, state)
        
        for holiday in holidays:
            holiday_date = self._parse_date_string(holiday.date, target_date.year)
            if target_date == holiday_date:
                return True
        
        return False
    
    def has_weekend_coverage(self, target_date: date, service_type: str) -> bool:
        """Check if provider has weekend coverage from configuration."""
        weekday = target_date.weekday()
        is_weekend = weekday >= 5  # Saturday (5) or Sunday (6)
        
        if not is_weekend:
            return True  # Not a weekend, so no issue
        
        coverage = self.get_weekend_coverage(service_type)
        
        if not coverage.get("enabled", False):
            return False
        
        # Check specific weekend days
        if weekday == 5:  # Saturday
            return coverage.get("saturday_coverage", False)
        elif weekday == 6:  # Sunday
            return coverage.get("sunday_coverage", False)
        
        return False
    
    def get_availability_summary(self, service_type: str, state: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive availability summary for a service."""
        return {
            "service_type": service_type,
            "state": state,
            "blackout_periods": self.get_blackout_periods(service_type, state),
            "holidays": self.get_holidays(service_type, state),
            "weekend_coverage": self.get_weekend_coverage(service_type)
        } 