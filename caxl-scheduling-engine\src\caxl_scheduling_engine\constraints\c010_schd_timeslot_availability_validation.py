"""
Time Slot Availability Validation Constraint (C010)

This constraint ensures that time slots are available for assignment.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from datetime import datetime
from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from caxl_scheduling_engine.model.planning_models import TimeSlotAssignment


def time_slot_availability(constraint_factory: ConstraintFactory) -> Constraint:
    """Time slot must be available for assignment."""
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: assignment.time_slot is not None)
            .filter(lambda assignment: not _is_provider_available_at_time(assignment))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Time slot availability"))


def _is_provider_available_at_time(assignment: TimeSlotAssignment) -> bool:
    """Check if provider is available at the assigned time slot."""
    if assignment.time_slot is None or assignment.scheduled_appointment.provider is None:
        return False
    
    provider = assignment.scheduled_appointment.provider
    assigned_date = assignment.scheduled_appointment.assigned_date
    assigned_time = assignment.time_slot
    
    # Create datetime for availability check
    check_datetime = datetime.combine(assigned_date, assigned_time)
    
    # Use ProviderAvailability.is_available_at_time() which includes break_periods check
    if provider.availability is not None:
        return provider.availability.is_available_at_time(check_datetime)
    
    # Fallback: basic business hours check if no availability configured
    hour = assigned_time.hour
    return 6 <= hour < 22