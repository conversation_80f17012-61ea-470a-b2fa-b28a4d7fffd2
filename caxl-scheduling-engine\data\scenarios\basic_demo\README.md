# Basic Demo Scenario

**Purpose**: Simple demonstration of core scheduling functionality
**Best for**: Initial demos, basic feature overview
**Complexity**: Low

## Features Demonstrated
- Basic appointment assignment
- Provider skill matching
- Geographic service areas
- Simple workload distribution

## Data Overview
- **Providers**: 3 (RN, LPN, CNA)
- **Patients**: 5
- **Appointments**: 8
- **Geographic Coverage**: Manhattan, NY

## Usage
```bash
# Copy this scenario to main data folder
cp -r data/scenarios/basic_demo/* data/

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results
- All appointments should be assigned
- Providers should be matched by skills
- Geographic constraints should be satisfied
- Basic workload balancing should be evident 